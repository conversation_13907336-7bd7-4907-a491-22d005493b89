/**
 * Exemplo de integração das cron jobs com o sistema de notificações
 * Demonstra como as notificações agora são enviadas através do sistema real
 */

import { processPaymentReminders, processOverdueNotifications } from '@/services/billing/notification-service'

/**
 * Exemplo de execução da cron job de lembretes de pagamento
 * Agora integrada com o sistema de notificações real
 */
export async function examplePaymentReminders() {
  console.log('🔔 Executando exemplo de lembretes de pagamento...')
  
  try {
    const result = await processPaymentReminders()
    
    if (result.success) {
      console.log('✅ Lembretes processados com sucesso:')
      console.log(`  - Total processados: ${result.data?.totalProcessed}`)
      console.log(`  - Lembretes enviados: ${result.data?.remindersSent}`)
      console.log(`  - Erros: ${result.data?.errors?.length || 0}`)
      
      // Mostrar detalhes dos lembretes enviados
      result.data?.reminders?.forEach(reminder => {
        console.log(`  📧 ${reminder.type} para ${reminder.student_email} (${reminder.days_until_due} dias)`)
      })
    } else {
      console.error('❌ Erro ao processar lembretes:', result.error)
    }
  } catch (error) {
    console.error('💥 Erro crítico:', error)
  }
}

/**
 * Exemplo de execução da cron job de notificações de atraso
 * Agora integrada com o sistema de notificações real
 */
export async function exampleOverdueNotifications() {
  console.log('🚨 Executando exemplo de notificações de atraso...')
  
  try {
    const result = await processOverdueNotifications()
    
    if (result.success) {
      console.log('✅ Notificações de atraso processadas com sucesso:')
      console.log(`  - Total processados: ${result.data?.totalProcessed}`)
      console.log(`  - Notificações enviadas: ${result.data?.notificationsSent}`)
      console.log(`  - Alertas admin enviados: ${result.data?.adminAlertsSent}`)
      console.log(`  - Erros: ${result.data?.errors?.length || 0}`)
      
      // Mostrar detalhes das notificações por nível de escalação
      const notificationsByLevel = result.data?.notifications?.reduce((acc, notification) => {
        if (notification.type === 'student_overdue') {
          const level = notification.escalation_level
          acc[level] = (acc[level] || 0) + 1
        }
        return acc
      }, {} as Record<number, number>) || {}
      
      console.log('📊 Notificações por nível de escalação:')
      Object.entries(notificationsByLevel).forEach(([level, count]) => {
        console.log(`  - Nível ${level}: ${count} notificações`)
      })
    } else {
      console.error('❌ Erro ao processar notificações de atraso:', result.error)
    }
  } catch (error) {
    console.error('💥 Erro crítico:', error)
  }
}

/**
 * Exemplo de execução completa (ambas as cron jobs)
 */
export async function exampleFullCronExecution() {
  console.log('🚀 Executando exemplo completo das cron jobs...')
  
  // Executar lembretes primeiro
  await examplePaymentReminders()
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // Depois executar notificações de atraso
  await exampleOverdueNotifications()
  
  console.log('\n✅ Execução completa finalizada!')
}

/**
 * Exemplo de como verificar o status das notificações enviadas
 */
export async function exampleNotificationStatus() {
  console.log('📊 Verificando status das notificações...')
  
  // Este exemplo mostra como você pode verificar o status das notificações
  // usando o NotificationDispatcher
  
  const { NotificationDispatcher } = await import('@/services/notifications/channels/notification-dispatcher')
  const dispatcher = new NotificationDispatcher()
  
  // Exemplo de verificação de status de entrega
  try {
    const status = await dispatcher.getDeliveryStatus('notification-id-example', 'email')
    console.log('Status da notificação:', status)
  } catch (error) {
    console.log('Erro ao verificar status:', error)
  }
}

/**
 * Exemplo de como enviar uma notificação manual usando o dispatcher
 */
export async function exampleManualNotification() {
  console.log('📤 Enviando notificação manual...')
  
  const { NotificationDispatcher } = await import('@/services/notifications/channels/notification-dispatcher')
  const dispatcher = new NotificationDispatcher()
  
  try {
    const result = await dispatcher.sendPaymentReminder({
      tenantId: 'tenant-example',
      userId: 'user-example',
      studentName: 'João Silva',
      amount: 150.00,
      dueDate: '2024-01-15',
      planName: 'Plano Mensal',
      channels: ['in_app', 'email']
    })
    
    if (result.success) {
      console.log('✅ Notificação manual enviada com sucesso!')
      console.log('ID da notificação:', result.notificationId)
      console.log('Resultados por canal:', result.channelResults)
    } else {
      console.error('❌ Erro ao enviar notificação manual:', result.errors)
    }
  } catch (error) {
    console.error('💥 Erro crítico:', error)
  }
}

// Exportar todas as funções de exemplo
export const cronIntegrationExamples = {
  paymentReminders: examplePaymentReminders,
  overdueNotifications: exampleOverdueNotifications,
  fullExecution: exampleFullCronExecution,
  notificationStatus: exampleNotificationStatus,
  manualNotification: exampleManualNotification
}
