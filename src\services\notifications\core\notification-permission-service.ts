/**
 * Serviço para verificar permissões de notificação
 * Verifica configurações do tenant e preferências do usuário
 */

import { createClient, createAdminClient } from '@/services/supabase/server';
import type { NotificationChannel, NotificationType } from '../types/notification-types';

export interface NotificationPermissionCheck {
  tenantId: string;
  userId: string;
  notificationType: NotificationType;
  channel: NotificationChannel;
}

export interface NotificationPermissionResult {
  allowed: boolean;
  reason?: string;
  source: 'tenant_global' | 'tenant_type' | 'user_preference' | 'default';
}

export interface UserNotificationPreference {
  notificationType: NotificationType;
  inAppEnabled: boolean;
  emailEnabled: boolean;
  whatsappEnabled: boolean;
}

export class NotificationPermissionService {
  /**
   * Verifica se uma notificação pode ser enviada por um canal específico
   */
  async checkPermission(check: NotificationPermissionCheck): Promise<NotificationPermissionResult> {
    try {
      const supabase = await createClient();

      // 1. Verificar configurações globais do tenant
      const { data: tenantSettings, error: tenantError } = await supabase
        .from('tenant_notification_settings')
        .select('email_enabled, whatsapp_enabled, notification_types')
        .eq('tenant_id', check.tenantId)
        .single();

      if (tenantError) {
        console.error('Erro ao buscar configurações do tenant:', tenantError);
        return {
          allowed: false,
          reason: 'Configurações do tenant não encontradas',
          source: 'tenant_global'
        };
      }

      // 2. Verificar se o canal está habilitado globalmente para o tenant
      if (check.channel === 'email' && !tenantSettings.email_enabled) {
        return {
          allowed: false,
          reason: 'E-mail desabilitado globalmente para este tenant',
          source: 'tenant_global'
        };
      }

      if (check.channel === 'whatsapp' && !tenantSettings.whatsapp_enabled) {
        return {
          allowed: false,
          reason: 'WhatsApp desabilitado globalmente para este tenant',
          source: 'tenant_global'
        };
      }

      // 3. Verificar configurações específicas por tipo de notificação
      const notificationTypes = tenantSettings.notification_types || {};
      const typeConfig = notificationTypes[check.notificationType];

      if (typeConfig && typeof typeConfig === 'object') {
        const channelEnabled = typeConfig[check.channel];
        
        if (channelEnabled === false) {
          return {
            allowed: false,
            reason: `${check.channel} desabilitado para notificações do tipo ${check.notificationType}`,
            source: 'tenant_type'
          };
        }
      }

      // 4. Verificar preferências do usuário
      // Usar cliente admin para bypass de RLS ao verificar preferências
      const adminSupabase = await createAdminClient();
      const { data: userPreference, error: prefError } = await adminSupabase
        .from('notification_preferences')
        .select('in_app_enabled, email_enabled, whatsapp_enabled')
        .eq('tenant_id', check.tenantId)
        .eq('user_id', check.userId)
        .eq('notification_type', check.notificationType)
        .single();

      // Se não há erro, significa que existe preferência do usuário
      if (!prefError && userPreference) {
        let userAllows = true;
        
        switch (check.channel) {
          case 'in_app':
            userAllows = userPreference.in_app_enabled;
            break;
          case 'email':
            userAllows = userPreference.email_enabled;
            break;
          case 'whatsapp':
            userAllows = userPreference.whatsapp_enabled;
            break;
        }

        if (!userAllows) {
          return {
            allowed: false,
            reason: `Usuário desabilitou ${check.channel} para notificações do tipo ${check.notificationType}`,
            source: 'user_preference'
          };
        }

        return {
          allowed: true,
          source: 'user_preference'
        };
      }

      // 5. Se não existe preferência do usuário, assumir que permite (conforme solicitado)
      return {
        allowed: true,
        source: 'default'
      };

    } catch (error) {
      console.error('Erro ao verificar permissões de notificação:', error);
      return {
        allowed: false,
        reason: error instanceof Error ? error.message : 'Erro desconhecido',
        source: 'tenant_global'
      };
    }
  }

  /**
   * Filtra canais permitidos para uma notificação
   */
  async getPermittedChannels(
    tenantId: string,
    userId: string,
    notificationType: NotificationType,
    requestedChannels: NotificationChannel[]
  ): Promise<NotificationChannel[]> {
    const permittedChannels: NotificationChannel[] = [];

    for (const channel of requestedChannels) {
      const permission = await this.checkPermission({
        tenantId,
        userId,
        notificationType,
        channel
      });

      if (permission.allowed) {
        permittedChannels.push(channel);
      } else {
        console.log(`Canal ${channel} não permitido: ${permission.reason}`);
      }
    }

    return permittedChannels;
  }

  /**
   * Obtém as preferências de notificação de um usuário
   */
  async getUserPreferences(
    tenantId: string,
    userId: string
  ): Promise<UserNotificationPreference[]> {
    try {
      const supabase = await createClient();

      const { data: preferences, error } = await supabase
        .from('notification_preferences')
        .select('notification_type, in_app_enabled, email_enabled, whatsapp_enabled')
        .eq('tenant_id', tenantId)
        .eq('user_id', userId);

      if (error) {
        console.error('Erro ao buscar preferências do usuário:', error);
        return [];
      }

      return preferences.map(pref => ({
        notificationType: pref.notification_type as NotificationType,
        inAppEnabled: pref.in_app_enabled,
        emailEnabled: pref.email_enabled,
        whatsappEnabled: pref.whatsapp_enabled
      }));

    } catch (error) {
      console.error('Erro ao buscar preferências do usuário:', error);
      return [];
    }
  }

  /**
   * Cria ou atualiza preferências de notificação de um usuário
   */
  async updateUserPreferences(
    tenantId: string,
    userId: string,
    notificationType: NotificationType,
    preferences: {
      inAppEnabled?: boolean;
      emailEnabled?: boolean;
      whatsappEnabled?: boolean;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await createClient();

      const updateData: any = {
        tenant_id: tenantId,
        user_id: userId,
        notification_type: notificationType,
        updated_at: new Date().toISOString()
      };

      if (preferences.inAppEnabled !== undefined) {
        updateData.in_app_enabled = preferences.inAppEnabled;
      }
      if (preferences.emailEnabled !== undefined) {
        updateData.email_enabled = preferences.emailEnabled;
      }
      if (preferences.whatsappEnabled !== undefined) {
        updateData.whatsapp_enabled = preferences.whatsappEnabled;
      }

      const { error } = await supabase
        .from('notification_preferences')
        .upsert(updateData, {
          onConflict: 'tenant_id,user_id,notification_type'
        });

      if (error) {
        console.error('Erro ao atualizar preferências:', error);
        return {
          success: false,
          error: error.message
        };
      }

      return { success: true };

    } catch (error) {
      console.error('Erro ao atualizar preferências do usuário:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Verifica se um usuário tem preferências configuradas
   */
  async hasUserPreferences(tenantId: string, userId: string): Promise<boolean> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from('notification_preferences')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('user_id', userId)
        .limit(1);

      if (error) {
        console.error('Erro ao verificar preferências do usuário:', error);
        return false;
      }

      return data && data.length > 0;

    } catch (error) {
      console.error('Erro ao verificar preferências do usuário:', error);
      return false;
    }
  }
}
