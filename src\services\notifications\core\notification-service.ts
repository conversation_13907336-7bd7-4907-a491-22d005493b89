/**
 * Serviço principal de notificações
 * Implementa as operações CRUD básicas para notificações in-app
 */

import { createClient } from '@/services/supabase/server';
import type {
  Notification,
  CreateNotificationData,
  NotificationFilters,
  PaginatedNotifications,
  NotificationServiceResponse,
  NotificationStats
} from '../types/notification-types';
import {
  CreateNotificationSchema,
  UpdateNotificationSchema,
  NotificationFiltersSchema
} from '../types/notification-schemas';

export class NotificationService {
  /**
   * Cria uma nova notificação
   */
  async create(tenantId: string, data: CreateNotificationData): Promise<NotificationServiceResponse<Notification>> {
    try {
      // Validar dados de entrada
      const validatedData = CreateNotificationSchema.parse(data);
      
      const supabase = await createClient();
      
      const { data: notification, error } = await supabase
        .from('notifications')
        .insert({
          tenant_id: tenantId,
          ...validatedData
        })
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar notificação:', error);
        return {
          success: false,
          error: `Erro ao criar notificação: ${error.message}`
        };
      }

      return {
        success: true,
        data: notification,
        message: 'Notificação criada com sucesso'
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Busca notificações do usuário com filtros e paginação
   */
  async getByUser(userId: string, filters?: NotificationFilters): Promise<NotificationServiceResponse<PaginatedNotifications>> {
    try {
      const validatedFilters = NotificationFiltersSchema.parse(filters || {});
      
      const supabase = await createClient();
      
      let query = supabase
        .from('notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', userId);

      // Aplicar filtros
      if (validatedFilters.status?.length) {
        query = query.in('status', validatedFilters.status);
      }
      
      if (validatedFilters.type?.length) {
        query = query.in('type', validatedFilters.type);
      }
      
      if (validatedFilters.category?.length) {
        query = query.in('category', validatedFilters.category);
      }
      
      if (validatedFilters.priority?.length) {
        query = query.in('priority', validatedFilters.priority);
      }
      
      if (validatedFilters.dateFrom) {
        query = query.gte('created_at', validatedFilters.dateFrom);
      }
      
      if (validatedFilters.dateTo) {
        query = query.lte('created_at', validatedFilters.dateTo);
      }
      
      if (validatedFilters.search) {
        query = query.or(`title.ilike.%${validatedFilters.search}%,message.ilike.%${validatedFilters.search}%`);
      }

      // Paginação
      const page = validatedFilters.page || 1;
      const limit = validatedFilters.limit || 20;
      const offset = (page - 1) * limit;

      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: notifications, error, count } = await query;

      if (error) {
        console.error('Erro ao buscar notificações:', error);
        return {
          success: false,
          error: `Erro ao buscar notificações: ${error.message}`
        };
      }

      const total = count || 0;
      const hasMore = offset + limit < total;

      return {
        success: true,
        data: {
          data: notifications || [],
          total,
          page,
          limit,
          hasMore
        }
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Marca uma notificação como lida
   */
  async markAsRead(notificationId: string, userId: string): Promise<NotificationServiceResponse<void>> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'read',
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Erro ao marcar notificação como lida:', error);
        return {
          success: false,
          error: `Erro ao marcar notificação como lida: ${error.message}`
        };
      }

      return {
        success: true,
        message: 'Notificação marcada como lida'
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Marca todas as notificações do usuário como lidas
   */
  async markAllAsRead(userId: string): Promise<NotificationServiceResponse<void>> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'read',
          read_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('status', 'unread');

      if (error) {
        console.error('Erro ao marcar todas as notificações como lidas:', error);
        return {
          success: false,
          error: `Erro ao marcar todas as notificações como lidas: ${error.message}`
        };
      }

      return {
        success: true,
        message: 'Todas as notificações foram marcadas como lidas'
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Arquiva uma notificação
   */
  async archive(notificationId: string, userId: string): Promise<NotificationServiceResponse<void>> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'archived',
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Erro ao arquivar notificação:', error);
        return {
          success: false,
          error: `Erro ao arquivar notificação: ${error.message}`
        };
      }

      return {
        success: true,
        message: 'Notificação arquivada com sucesso'
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Deleta uma notificação (soft delete)
   */
  async delete(notificationId: string, userId: string): Promise<NotificationServiceResponse<void>> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('notifications')
        .update({
          status: 'deleted',
          updated_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Erro ao deletar notificação:', error);
        return {
          success: false,
          error: `Erro ao deletar notificação: ${error.message}`
        };
      }

      return {
        success: true,
        message: 'Notificação deletada com sucesso'
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Conta notificações não lidas do usuário
   */
  async getUnreadCount(userId: string): Promise<NotificationServiceResponse<number>> {
    try {
      const supabase = await createClient();

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('status', 'unread');

      if (error) {
        console.error('Erro ao contar notificações não lidas:', error);
        return {
          success: false,
          error: `Erro ao contar notificações não lidas: ${error.message}`
        };
      }

      return {
        success: true,
        data: count || 0
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Obtém estatísticas de notificações do usuário
   */
  async getStats(userId: string): Promise<NotificationServiceResponse<NotificationStats>> {
    try {
      const supabase = await createClient();

      const { data: notifications, error } = await supabase
        .from('notifications')
        .select('status, type, category, priority')
        .eq('user_id', userId);

      if (error) {
        console.error('Erro ao buscar estatísticas:', error);
        return {
          success: false,
          error: `Erro ao buscar estatísticas: ${error.message}`
        };
      }

      const stats: NotificationStats = {
        total: notifications?.length || 0,
        unread: 0,
        read: 0,
        archived: 0,
        byType: {
          payment: 0,
          class: 0,
          system: 0,
          enrollment: 0,
          enrollment_payment_due: 0,
          expense_due: 0,
          new_enrollment_notification: 0,
          event: 0
        },
        byCategory: {
          reminder: 0,
          alert: 0,
          info: 0,
          success: 0,
          error: 0
        },
        byPriority: {
          low: 0,
          medium: 0,
          high: 0,
          urgent: 0
        }
      };

      notifications?.forEach(notification => {
        // Contar por status
        if (notification.status === 'unread') stats.unread++;
        else if (notification.status === 'read') stats.read++;
        else if (notification.status === 'archived') stats.archived++;

        // Contar por tipo
        stats.byType[notification.type as keyof typeof stats.byType]++;

        // Contar por categoria
        stats.byCategory[notification.category as keyof typeof stats.byCategory]++;

        // Contar por prioridade
        stats.byPriority[notification.priority as keyof typeof stats.byPriority]++;
      });

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Agenda uma notificação para envio futuro
   */
  async scheduleNotification(tenantId: string, data: CreateNotificationData): Promise<NotificationServiceResponse<Notification>> {
    try {
      if (!data.scheduled_for) {
        return {
          success: false,
          error: 'Data de agendamento é obrigatória para notificações agendadas'
        };
      }

      // Verificar se a data é no futuro
      const scheduledDate = new Date(data.scheduled_for);
      const now = new Date();

      if (scheduledDate <= now) {
        return {
          success: false,
          error: 'Data de agendamento deve ser no futuro'
        };
      }

      return await this.create(tenantId, data);

    } catch (error) {
      console.error('Erro no serviço de notificações:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }
}
